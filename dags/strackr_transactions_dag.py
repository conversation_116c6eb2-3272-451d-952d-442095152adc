"""
Simplified Strackr Transaction Reports DAG

This DAG fetches transactions from Strackr API, transforms them to a normalized schema,
and stores them in Supabase.

Schedule: Daily at 2 AM UTC
Catchup: False (only process current data)
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from datetime import datetime, timedelta
import logging

# Import our simplified functions
from dependencies.strackr import (
    fetch_strackr_transactions,
    transform_strackr_transactions,
    upload_to_supabase,
)

logger = logging.getLogger(__name__)

# Log DAG initialization
logger.info("=" * 80)
logger.info("🚀 INITIALIZING STRACKR TRANSACTIONS DAG")
logger.info("=" * 80)

# DAG Configuration
default_args = {
    "owner": "data-team",
    "depends_on_past": False,
    "start_date": datetime(2024, 1, 1),
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timed<PERSON>ta(minutes=5),
    "email": ["<EMAIL>"],
}

logger.info("📋 DAG Configuration:")
logger.info(f"   • Owner: {default_args['owner']}")
logger.info(f"   • Start Date: {default_args['start_date']}")
logger.info(f"   • Retries: {default_args['retries']}")
logger.info(f"   • Retry Delay: {default_args['retry_delay']}")
logger.info(f"   • Email Notifications: {default_args['email']}")

# Create the DAG
dag = DAG(
    "strackr_transaction_reports",
    default_args=default_args,
    description="Simplified Strackr transaction processing",
    schedule_interval="0 2 * * *",  # Daily at 2 AM UTC
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["transaction", "strackr", "simple"],
    max_active_runs=1,
)

logger.info("📊 DAG Settings:")
logger.info(f"   • DAG ID: {dag.dag_id}")
logger.info(f"   • Schedule: {dag.schedule_interval} (Daily at 2 AM UTC)")
logger.info(f"   • Catchup: {dag.catchup}")
logger.info(f"   • Max Active Runs: {dag.max_active_runs}")
logger.info(f"   • Tags: {dag.tags}")


def log_task_start(task_name: str, **context):
    """Log task start with context information."""
    logger.info("=" * 80)
    logger.info(f"🏁 TASK START: {task_name}")
    logger.info("=" * 80)
    logger.info(f"   • Execution Date: {context.get('execution_date')}")
    logger.info(f"   • DAG Run ID: {context.get('run_id')}")
    logger.info(f"   • Task Instance: {context.get('task_instance')}")


def log_task_completion(task_name: str, result: dict, **context):
    """Log task completion with results."""
    success = result.get('success', False)
    execution_time = result.get('task_execution_time', 0)

    if success:
        logger.info("=" * 80)
        logger.info(f"✅ TASK COMPLETED: {task_name} in {execution_time:.2f} seconds")
        logger.info("=" * 80)
    else:
        logger.error("=" * 80)
        logger.error(f"❌ TASK FAILED: {task_name} after {execution_time:.2f} seconds")
        logger.error(f"   • Error: {result.get('error', 'Unknown error')}")
        logger.error("=" * 80)


# Task 1: Fetch transactions from Strackr API
fetch_task = PythonOperator(
    task_id="fetch_strackr_transactions",
    python_callable=fetch_strackr_transactions,
    dag=dag,
    doc_md="""
    ## Fetch Strackr Transactions

    Fetches transactions from Strackr API for yesterday's date.

    **What it does:**
    - Gets yesterday's date range
    - Calls Strackr API with authentication
    - Handles pagination automatically
    - Returns raw transaction data

    **Logging:**
    - API request/response details
    - Pagination progress
    - Transaction statistics
    - Performance metrics
    """,
)

# Task 2: Transform transactions to normalized format
transform_task = PythonOperator(
    task_id="transform_strackr_transactions",
    python_callable=transform_strackr_transactions,
    dag=dag,
    doc_md="""
    ## Transform Strackr Transactions

    Transforms raw Strackr data to normalized format using Pydantic models.

    **What it does:**
    - Takes raw transactions from fetch task
    - Validates and transforms each transaction
    - Filters out invalid transactions
    - Returns normalized transaction data

    **Logging:**
    - Transformation progress by batches
    - Validation errors and warnings
    - Data quality statistics
    - Performance metrics
    """,
)

# Task 3: Upload to Supabase
upload_task = PythonOperator(
    task_id="upload_to_supabase",
    python_callable=upload_to_supabase,
    dag=dag,
    doc_md="""
    ## Upload to Supabase

    Uploads normalized transactions to Supabase database.

    **What it does:**
    - Takes normalized transactions from transform task
    - Uploads to Supabase in batches
    - Handles duplicates with upsert
    - Provides upload statistics

    **Logging:**
    - Batch upload progress
    - Database connection details
    - Upload statistics and performance
    - Error handling and recovery
    """,
)

# Define task dependencies - simple linear flow
fetch_task >> transform_task >> upload_task

logger.info("🔗 Task Dependencies Configured:")
logger.info("   fetch_strackr_transactions >> transform_strackr_transactions >> upload_to_supabase")

# Add DAG documentation
dag.doc_md = """
# Simplified Strackr Transaction DAG

This is a simplified version of the Strackr transaction processing DAG with comprehensive logging.

## Key Improvements

- **3 files instead of 11**: Reduced complexity dramatically
- **Simple linear flow**: fetch → transform → upload
- **Pydantic validation**: Clean data models with automatic validation
- **Environment-based config**: No complex configuration files
- **Inline authentication**: No separate auth modules
- **Comprehensive logging**: Detailed logs for production debugging

## Data Flow

1. **Fetch**: Get yesterday's transactions from Strackr API
2. **Transform**: Convert to normalized format with validation
3. **Upload**: Store in Supabase with duplicate handling

## Logging Features

### API Call Logging
- Request/response details for all Strackr API calls
- Pagination progress and statistics
- Error handling with retry information
- Performance metrics and throughput

### Task Execution Logging
- Task start/completion with timing
- Progress updates during processing
- Data quality and validation statistics
- Detailed error reporting with context

### Database Operation Logging
- Supabase connection and initialization
- Batch upload progress and statistics
- Duplicate handling and conflict resolution
- Performance metrics and success rates

## Dependencies

- Strackr API credentials in environment variables or Secret Manager
- Supabase connection configured via environment variables
- Required packages: requests, pydantic, supabase-py

## Monitoring

- Email notifications on failures
- Detailed logging for each step with emojis for easy scanning
- Task-level error handling and recovery
- Performance metrics for optimization
- Data quality statistics for monitoring

## Debugging in Production

The enhanced logging provides:
- **API Call Tracing**: Every API request/response logged with timing
- **Data Flow Visibility**: Complete transaction flow with sample data
- **Error Context**: Detailed error information with transaction IDs
- **Performance Metrics**: Timing and throughput for each step
- **Data Quality Insights**: Validation failures and warnings

Use log filters in Airflow UI to focus on specific log levels:
- `INFO`: General progress and statistics
- `WARNING`: Data quality issues and non-fatal errors
- `ERROR`: Task failures and critical issues
- `DEBUG`: Detailed transaction-level information
"""

logger.info("📚 DAG Documentation Updated")
logger.info("✅ Strackr Transactions DAG Initialization Complete")
logger.info("=" * 80)

if __name__ == "__main__":
    logger.info("🧪 Running DAG test...")
    dag.test()
