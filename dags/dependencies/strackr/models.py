"""
Simple data models for Strackr transactions using Pydantic.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator
import logging

logger = logging.getLogger(__name__)


class StrackrTransaction(BaseModel):
    """Model for Strackr transaction data matching normalized_transactions table schema."""

    # Core identifiers (required)
    transaction_id: str = Field(
        ..., description="Unique transaction ID (strackr_originalid)"
    )
    platform: str = Field(default="strackr", description="Platform identifier")
    source_transaction_id: str = Field(
        ..., description="Original Strackr transaction ID"
    )
    order_id: str = Field(default="", description="Order ID from merchant")

    # Dates (required)
    transaction_date: datetime = Field(..., description="When the transaction occurred")
    created_date: datetime = Field(..., description="When record was created")
    last_updated: datetime = Field(..., description="When record was last updated")

    # Financial data (required)
    currency: str = Field(default="USD", description="Currency code")
    order_amount: float = Field(..., description="Order amount")
    commission_amount: float = Field(..., description="Commission amount")

    # Merchant/Network info (required)
    network_name: str = Field(..., description="Affiliate network name")
    merchant_name: str = Field(..., description="Merchant name")

    # Status (required)
    status: str = Field(..., description="Transaction status")

    # Optional fields matching the exact schema
    final_order_amount: Optional[float] = None
    final_commission_amount: Optional[float] = None
    customer_id: Optional[str] = None
    merchant_id: Optional[str] = None
    connection_name: Optional[str] = None
    transaction_type: Optional[str] = None
    decline_reason: Optional[str] = None
    channel_name: Optional[str] = None  # Added to match schema
    custom_fields: Optional[Dict[str, Any]] = None
    comments: Optional[str] = None

    # Additional fields from schema (will be auto-populated by DB)
    # id: UUID (auto-generated by DB)
    # created_at: timestamp (auto-populated by DB)
    # updated_at: timestamp (auto-populated by DB)
    # upload_batch_id: Optional[UUID] = None
    # source_file: Optional[str] = None
    # csv_row_number: Optional[int] = None
    # transaction_fingerprint: Optional[str] = None

    @validator("order_amount", "commission_amount")
    def validate_amounts(cls, v):
        """Ensure amounts are non-negative."""
        if v < 0:
            raise ValueError("Amounts must be non-negative")
        return v

    @validator("currency")
    def validate_currency(cls, v):
        """Ensure currency is uppercase."""
        return v.upper()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database insertion."""
        data = self.dict()

        # Convert datetime objects to ISO strings for JSON serialization
        for field in ["transaction_date", "created_date", "last_updated"]:
            if data[field]:
                data[field] = data[field].isoformat()

        return data


def transform_strackr_data(
    raw_transaction: Dict[str, Any],
) -> Optional[StrackrTransaction]:
    """
    Transform raw Strackr API data to our model.

    Args:
        raw_transaction: Raw transaction data from Strackr API

    Returns:
        StrackrTransaction instance or None if transformation fails
    """
    tx_id = raw_transaction.get("id", "unknown")
    logger.info(f"🔄 Starting transformation for transaction {tx_id}")

    try:
        # Extract core data
        source_id = str(raw_transaction["id"])
        transaction_id = f"strackr_{source_id}"
        logger.info(f"   • Source ID: {source_id} -> Transaction ID: {transaction_id}")

        # Parse dates with detailed logging
        logger.info("   • Parsing transaction dates...")
        transaction_date = _parse_datetime(raw_transaction["sold_at"])
        if not transaction_date:
            logger.warning(
                f"   ⚠️  Failed to parse sold_at date for TX {tx_id}: {raw_transaction.get('sold_at')}"
            )

        created_date = (
            _parse_datetime(raw_transaction.get("clicked_at")) or transaction_date
        )
        last_updated = (
            _parse_datetime(raw_transaction.get("status_updated_at")) or created_date
        )

        logger.info(f"   • Transaction date: {transaction_date}")
        logger.info(f"   • Created date: {created_date}")
        logger.info(f"   • Last updated: {last_updated}")

        # Extract financial data with validation
        logger.info("   • Extracting financial data...")
        order_amount = float(raw_transaction.get("order_amount", 0))
        # Use 'revenue' field for commission amount (not 'commission_amount')
        commission_amount = float(raw_transaction.get("revenue", 0))

        logger.info(f"   • Order amount: ${order_amount}")
        logger.info(
            f"   • Commission amount: ${commission_amount} (from 'revenue' field)"
        )

        if order_amount < 0 or commission_amount < 0:
            logger.warning(
                f"   ⚠️  Negative amounts detected for TX {tx_id}: order=${order_amount}, commission=${commission_amount}"
            )

        # Extract merchant and network info
        network_name = raw_transaction.get("network_name", "")
        merchant_name = raw_transaction.get("advertiser_name", "Unknown")

        logger.info(f"   • Network: {network_name}")
        logger.info(f"   • Merchant: {merchant_name}")

        if not network_name:
            logger.warning(f"   ⚠️  Missing network_name for TX {tx_id}")
        if merchant_name == "Unknown":
            logger.warning(f"   ⚠️  Missing advertiser_name for TX {tx_id}")

        # Extract and normalize status
        raw_status = raw_transaction.get("status_id", "")
        normalized_status = _normalize_status(raw_status)
        logger.info(f"   • Status: {raw_status} -> {normalized_status}")

        # Create the transaction
        logger.info("   • Creating StrackrTransaction object...")
        transaction = StrackrTransaction(
            transaction_id=transaction_id,
            source_transaction_id=source_id,
            order_id=raw_transaction.get("order_id") or "",
            transaction_date=transaction_date,
            created_date=created_date,
            last_updated=last_updated,
            currency=raw_transaction.get("currency", "USD"),
            order_amount=order_amount,
            commission_amount=commission_amount,
            network_name=network_name,
            merchant_name=merchant_name,
            status=normalized_status,
            final_order_amount=raw_transaction.get("final_order_amount"),
            final_commission_amount=raw_transaction.get("final_commission_amount"),
            customer_id=raw_transaction.get("customer_id"),
            merchant_id=raw_transaction.get("advertiser_id"),
            connection_name=raw_transaction.get("connection_name"),
            transaction_type=raw_transaction.get("type_id", "transaction"),
            decline_reason=_extract_decline_reason(raw_transaction),
            channel_name=raw_transaction.get("channel_name"),  # Added channel_name
            custom_fields=raw_transaction.get("custom_fields"),
            comments=raw_transaction.get("comments"),
        )

        logger.info(f"✅ Successfully transformed transaction {tx_id}")
        return transaction

    except KeyError as e:
        logger.error(f"❌ Missing required field for transaction {tx_id}: {e}")
        logger.info(f"   Available fields: {list(raw_transaction.keys())}")
        return None
    except ValueError as e:
        logger.error(f"❌ Invalid value in transaction {tx_id}: {e}")
        logger.info(f"   Raw transaction: {raw_transaction}")
        return None
    except Exception as e:
        logger.error(f"❌ Unexpected error transforming transaction {tx_id}: {e}")
        logger.error(f"   Exception type: {type(e).__name__}")
        logger.info(f"   Raw transaction: {raw_transaction}")
        return None


def _parse_datetime(date_str: Optional[str]) -> Optional[datetime]:
    """Parse datetime string from Strackr API."""
    if not date_str:
        logger.info(f"   ⚠️  Empty date string provided")
        return None

    logger.info(f"   🔄 Parsing datetime: '{date_str}'")

    try:
        # Handle different datetime formats from Strackr
        if "T" in date_str:
            if date_str.endswith("Z"):
                result = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
                logger.info(f"   ✅ Parsed ISO format with Z: {result}")
                return result
            else:
                result = datetime.fromisoformat(date_str)
                logger.info(f"   ✅ Parsed ISO format: {result}")
                return result
        else:
            result = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            logger.info(f"   ✅ Parsed standard format: {result}")
            return result
    except Exception as e:
        logger.warning(f"   ❌ Failed to parse datetime '{date_str}': {e}")
        return None


def _normalize_status(status_id: Any) -> str:
    """Normalize Strackr status to standard values."""
    logger.info(
        f"   🔄 Normalizing status: {status_id} (type: {type(status_id).__name__})"
    )

    if not status_id:
        logger.info(f"   ⚠️  Empty status_id, returning 'unknown'")
        return "unknown"

    status_map = {
        1: "pending",
        2: "confirmed",
        3: "declined",
        "pending": "pending",
        "confirmed": "confirmed",
        "declined": "declined",
    }

    normalized = status_map.get(status_id, str(status_id).lower())
    logger.info(f"   ✅ Status normalized: {status_id} -> {normalized}")
    return normalized


def _extract_decline_reason(raw_transaction: Dict[str, Any]) -> Optional[str]:
    """Extract decline reason from raw transaction data."""
    reason = raw_transaction.get("reason")
    logger.info(f"   🔄 Extracting decline reason: {type(reason).__name__}")

    if reason and isinstance(reason, dict):
        decline_reason = reason.get("name")
        logger.info(f"   ✅ Decline reason extracted: {decline_reason}")
        return decline_reason
    elif reason:
        logger.info(f"   ⚠️  Reason is not a dict: {reason}")
    else:
        logger.info(f"   ℹ️  No reason provided")

    return None
