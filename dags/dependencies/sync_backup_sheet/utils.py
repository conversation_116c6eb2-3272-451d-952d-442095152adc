from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from datetime import datetime
import csv
from google.auth import default
from google.cloud import storage
from googleapiclient.discovery import build
import logging
import os
from google.cloud import storage, bigtable
from google.cloud.bigtable.row import DirectRow
from googleapiclient.discovery import build

from dependencies.sync_backup_sheet.constants import (
    BRANDS_BUCKET,
    BRANDS_BLOB,
    BIGTABLE_PROJECT,
    BIGTABLE_INSTANCE,
    BIGTABLE_TABLE,
    COLUMN_FAMILY,
    DOMAIN_QUALIFIER,
    ALIAS_QUALIFIER,
    GSHEET_API_KEY
)

# Configure logging

logger = logging.getLogger(__name__)

def convert_to_csv(data):
    """Convert a list of lists to a CSV string."""
    from io import StringIO
    output = StringIO()
    writer = csv.writer(output)
    writer.writerows(data)
    return output.getvalue()

def upload_to_gcs(bucket_name, filename, content):
    """Upload a file to GCS."""
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(filename)
        blob.upload_from_string(content, content_type='text/csv')
        logger.info(f"Uploaded {filename} to bucket {bucket_name}.")
    except Exception as e:
        raise Exception(f"Failed to upload {filename} to GCS: {str(e)}")
    
def buildBrandDomainRowKey(domain):
    """Build a Bigtable row key for a brand domain."""
    return f"brand_domain#{domain.strip().lower()}"

def buildBrandAliasRowKey(alias):
    """Build a Bigtable row key for a brand alias."""
    return f"brand_alias#{alias.strip().lower()}"

def backup_sheet_to_gcs(sheet_type, spreadsheet_id, data_range='A:A', **kwargs):
    IS_DEV = (os.getenv('ENVIRONMENT') != 'prod')
    logger.info(f"IS_DEV: {IS_DEV}")
    print(f"IS_DEV: {IS_DEV}")
    try:
        # Set up Google Sheets API client
        sheets_service = build("sheets", "v4", developerKey=GSHEET_API_KEY)
        sheet = sheets_service.spreadsheets()
        result = sheet.values().get(spreadsheetId=spreadsheet_id, range=data_range).execute()
        rows = result.get("values", [])
        logger.info(f"Retrieved {len(rows)} rows from {sheet_type} sheet.")
        if not rows:
            logger.warning(f"No data found for {sheet_type} in range {data_range}.")
            return

        # Convert rows to CSV
        csv_content = convert_to_csv(rows)
        logger.info(f"*******CSV content: {csv_content}")
        # Generate filenames
        timestamp = datetime.now().strftime("%Y-%m-%d")
        base_filename = f"{sheet_type}_websites.csv"
        versioned_filename = f"{sheet_type}_websites-{timestamp}.csv"
        bucket_name = f"{sheet_type}-websites"

        # Upload files to GCS
        try:
            upload_to_gcs(bucket_name, base_filename, csv_content)
            logger.info(f"Uploaded {base_filename} to bucket {bucket_name}.")

        except Exception as e:
            return f"Error uploading {base_filename}: {str(e)}", 500

        try:
            upload_to_gcs(bucket_name, versioned_filename, csv_content)
            logger.info(f"Uploaded {versioned_filename} to bucket {bucket_name}.")

        except Exception as e:
            return f"Error uploading {versioned_filename}: {str(e)}", 500

        logger.info(f"Backup for {sheet_type} completed successfully.")
        return "Backup completed successfully.", 200

    except Exception as e:
        return f"Error in backup_sheet_to_gcs: {str(e)}", 500

def update_brand_alias_mappings():
    """Read brand-domain-alias CSV from GCS, clean, and upsert into Bigtable."""
    storage_client = storage.Client()
    bucket = storage_client.bucket(BRANDS_BUCKET)
    blob = bucket.blob(BRANDS_BLOB)
    content = blob.download_as_text()
    reader = csv.reader(content.splitlines())
    rows = list(reader)[1:]# skip header

    # Clean and build maps
    domain_map, alias_map = {}, {}
    for row in rows:
        brand = row[0].strip() if len(row) > 0 else ''
        raw_domain = row[1].strip().lower() if len(row) > 1 else ''
        raw_aliases = row[2] if len(row) > 2 else ''

        # domain cleanup
        if raw_domain:
            d = raw_domain[4:] if raw_domain.startswith('www.') else raw_domain
            domain_map[d] = brand
            parts = d.split('.')
            if len(parts) >= 3 and parts[-2] == 'co':
                combo = '.'.join(parts[-3:])
                domain_map[combo] = brand
                domain_map[parts[-3]] = brand
            else:
                combo = '.'.join(parts[-2:])
                domain_map[combo] = brand
                if len(parts) >= 2:
                    domain_map[parts[-2]] = brand

        # alias cleanup
        if raw_aliases:
            for alias in raw_aliases.split(','):
                a = alias.strip().lower()
                if a:
                    alias_map[a] = brand

    # update and insert into Bigtable
    creds, _ = default()
    bt_client = bigtable.Client(project=BIGTABLE_PROJECT, credentials=creds, admin=True)
    instance = bt_client.instance(BIGTABLE_INSTANCE)
    table = instance.table(BIGTABLE_TABLE)

    mutations = []
    idx = 0
    for domain, brand in domain_map.items():
        brandDomainRowKey = buildBrandDomainRowKey(domain)
        row = DirectRow(brandDomainRowKey.encode())
        row.set_cell(COLUMN_FAMILY, DOMAIN_QUALIFIER, brand.encode())
        mutations.append(row)
        logger.info(f"{idx} Adding domain mapping: {brandDomainRowKey} -> {brand}")
        idx += 1

    idx = 0
    for alias, brand in alias_map.items():
        brandAliasRowKey = buildBrandAliasRowKey(alias)
        row = DirectRow(brandAliasRowKey.encode())
        row.set_cell(COLUMN_FAMILY, ALIAS_QUALIFIER, brand.encode())
        mutations.append(row)
        logger.info(f"{idx} Adding alias mapping: {brandAliasRowKey} -> {brand}")
        idx += 1

    if mutations:
        table.mutate_rows(mutations)
        logger.info(f"Synced {len(mutations)} mappings to Bigtable")