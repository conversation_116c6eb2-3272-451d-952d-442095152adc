"""
Apple Analytics Integration Module

This module provides comprehensive services for interacting with Apple App Store Connect API
to fetch analytics reports, transform data, and upload to Supabase.

Components:
- apple_api_client: Main API client for Apple Analytics
- jwt_service: JWT token generation and management
- apple_config: Configuration constants and report targeting
- apple_models: Pydantic models for data transformation
- apple_tasks: Airflow task functions for the DAG
"""

from .jwt_service import AppleJWTService
from .apple_api_client import AppleAnalyticsClient
from .apple_config import (
    TARGET_REPORTS,
    get_enabled_reports,
    get_report_config,
    get_reports_by_category,
    ReportCategory,
    ReportConfig,
)
from .apple_models import (
    AppleAnalyticsRawData,
    AppleAnalyticsNormalized,
    transform_apple_analytics_data,
    transform_csv_data,
    parse_csv_row,
)
from .apple_tasks import (
    fetch_apple_analytics_data,
    transform_apple_analytics_data,
    upload_to_supabase,
    validate_apple_credentials,
)

__all__ = [
    # Main services
    "AppleJWTService", 
    "AppleAnalyticsClient",
    
    # Configuration
    "TARGET_REPORTS",
    "get_enabled_reports",
    "get_report_config",
    "get_reports_by_category",
    "ReportCategory",
    "ReportConfig",
    
    # Data models
    "AppleAnalyticsRawData",
    "AppleAnalyticsNormalized", 
    "transform_apple_analytics_data",
    "transform_csv_data",
    "parse_csv_row",
    
    # Task functions
    "fetch_apple_analytics_data",
    "transform_apple_analytics_data",
    "upload_to_supabase",
    "validate_apple_credentials",
]
