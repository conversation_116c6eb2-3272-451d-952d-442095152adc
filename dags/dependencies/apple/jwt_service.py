"""
Apple JWT Token Service

This service handles JWT token generation for Apple App Store Connect API authentication.
Tokens are valid for 20 minutes and use ES256 algorithm as required by Apple.
"""

import jwt
import time
import os
import logging
from typing import Optional
from datetime import datetime, timedelta
from google.cloud import secretmanager

logger = logging.getLogger(__name__)


class AppleJWTService:
    """Service for generating Apple App Store Connect API JWT tokens."""

    def __init__(self):
        """Initialize the JWT service with credentials from environment variables or Secret Manager."""
        logger.info("🔐 Initializing Apple JWT service...")
        logger.info("🔑 Fetching Apple JWT credentials...")
        
        self.key_id = self._get_credential("APPLE_KEY_ID", "apple-key-id")
        self.issuer_id = self._get_credential("APPLE_ISSUER_ID", "apple-issuer-id")
        self.private_key_content = self._get_credential("APPLE_PRIVATE_KEY_PATH", "apple-private-key-path")

        # Log credential status
        logger.info("📋 JWT credential status:")
        logger.info(f"   • Key ID present: {bool(self.key_id)} - {self.key_id[:10] if self.key_id else 'Missing'}...")
        logger.info(f"   • Issuer ID present: {bool(self.issuer_id)} - {self.issuer_id[:10] if self.issuer_id else 'Missing'}...")
        logger.info(f"   • Private Key present: {bool(self.private_key_content)} - {len(self.private_key_content) if self.private_key_content else 0} characters")

        # Validate required credentials
        if not all([self.key_id, self.issuer_id, self.private_key_content]):
            missing_vars = []
            if not self.key_id:
                missing_vars.append("APPLE_KEY_ID")
            if not self.issuer_id:
                missing_vars.append("APPLE_ISSUER_ID")
            if not self.private_key_content:
                missing_vars.append("APPLE_PRIVATE_KEY_PATH")

            logger.error(f"❌ Missing JWT credentials: {', '.join(missing_vars)}")
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}"
            )
        
        logger.info("✅ Apple JWT service initialized successfully")

    def _get_credential(self, env_var: str, secret_name: str) -> str:
        """Get credential from environment variable or Secret Manager."""
        logger.info(f"Getting credential for {secret_name}...")

        # Try environment variable first
        value = os.getenv(env_var)
        if value:
            logger.info(f"✅ Retrieved credential {secret_name} from environment variable")
            return value
            
        # Try Secret Manager
        try:
            logger.info(f"🔍 Attempting to retrieve credential {secret_name} from Secret Manager")
            start_time = time.time()

            client = secretmanager.SecretManagerServiceClient()
            project_id = "609155540540"
            secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

            logger.info(f"Secret path: {secret_path}")
            response = client.access_secret_version(request={"name": secret_path})

            secret_time = time.time() - start_time
            logger.info(f"✅ Successfully retrieved credential {secret_name} from Secret Manager in {secret_time:.2f} seconds")

            return response.payload.data.decode("UTF-8")

        except Exception as e:
            logger.error(f"❌ Failed to get credential {secret_name} from Secret Manager: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            return ""

    def _load_private_key(self) -> str:
        """
        Load the private key content.

        Returns:
            str: The private key content

        Raises:
            ValueError: If the private key content is empty
        """
        try:
            if not self.private_key_content.strip():
                raise ValueError("Private key content is empty")

            logger.info("Successfully loaded private key content")
            return self.private_key_content

        except Exception as e:
            logger.error(f"Error loading private key: {e}")
            raise

    def generate_jwt_token(self) -> Optional[str]:
        """
        Generate a JWT token for Apple App Store Connect API.

        The token is valid for 20 minutes (maximum allowed by Apple) and uses
        the ES256 algorithm as required by Apple's API.

        Returns:
            Optional[str]: JWT token string if successful, None if failed

        Raises:
            Exception: If token generation fails
        """
        try:
            # Load the private key
            private_key = self._load_private_key()

            # JWT expiration time (20 minutes from now - maximum allowed by Apple)
            expiration_time = int(time.time()) + (20 * 60)

            # JWT payload
            payload = {
                "iss": self.issuer_id,  # Issuer ID
                "exp": expiration_time,  # Expiration time
                "aud": "appstoreconnect-v1",  # Audience (fixed for App Store Connect)
            }

            # Generate JWT token
            token = jwt.encode(
                payload,
                private_key,
                algorithm="ES256",  # Apple requires ES256 algorithm
                headers={"kid": self.key_id},  # Key ID in header
            )

            expiry_datetime = datetime.fromtimestamp(expiration_time)
            logger.info(
                f"Successfully generated JWT token. Expires at: {expiry_datetime}"
            )

            return token

        except Exception as e:
            logger.error(f"Error generating JWT token: {e}")
            raise

    def is_token_valid(self, token: str) -> bool:
        """
        Check if a JWT token is still valid (not expired).

        Args:
            token (str): The JWT token to validate

        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            # Decode without verification to check expiration
            decoded = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = decoded.get("exp")

            if not exp_timestamp:
                return False

            # Check if token is expired (with 1 minute buffer)
            current_time = int(time.time())
            return exp_timestamp > (current_time + 60)

        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return False

    def get_token_expiry(self, token: str) -> Optional[datetime]:
        """
        Get the expiration datetime of a JWT token.

        Args:
            token (str): The JWT token

        Returns:
            Optional[datetime]: Expiration datetime if successful, None otherwise
        """
        try:
            decoded = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = decoded.get("exp")

            if exp_timestamp:
                return datetime.fromtimestamp(exp_timestamp)
            return None

        except Exception as e:
            logger.error(f"Error getting token expiry: {e}")
            return None
