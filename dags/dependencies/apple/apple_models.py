"""
Apple Analytics Data Models

This module defines Pydantic models for Apple Analytics data transformation,
including raw data validation and normalized data structures for Supabase.
"""

import hashlib
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
from pydantic.dataclasses import dataclass

logger = logging.getLogger(__name__)


# Base class for common fields
class AppleAnalyticsBaseData(BaseModel):
    """Base class for Apple Analytics data with common fields."""
    
    date: str = Field(..., description="Report date in YYYY-MM-DD format")
    app_name: str = Field(..., description="Application name")
    app_apple_identifier: str = Field(..., description="Apple App Store ID")
    app_version: str = Field(..., description="App version")
    device: str = Field(..., description="Device type (iPhone, iPad, etc.)")
    platform_version: str = Field(..., description="iOS/iPadOS version")
    source_type: str = Field(..., description="Traffic source type")
    territory: str = Field(..., description="Country/territory code")
    counts: int = Field(..., description="Event count")
    
    @validator('date', pre=True)
    def validate_date(cls, v):
        """Validate date format."""
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError(f"Invalid date format: {v}")

    @validator('counts', pre=True)
    def validate_positive_counts(cls, v):
        """Ensure counts are positive."""
        if v < 0:
            raise ValueError("Counts must be non-negative")
        return v

    @validator('app_apple_identifier', pre=True) 
    def validate_app_id(cls, v):
        """Validate Apple App ID format."""
        if not v.isdigit() or len(v) < 8:
            raise ValueError(f"Invalid Apple App ID: {v}")
        return v
    
    class Config:
        """Pydantic configuration."""
        str_strip_whitespace = True
        validate_assignment = True

# Model for Installation/Deletion Detailed reports
class AppleInstallationData(AppleAnalyticsBaseData):
    """Raw Apple Installation/Deletion data model."""
    
    event: str = Field(..., description="Event type (Install, Delete, etc.)")
    download_type: Optional[str] = Field(None, description="Type of download")
    source_info: Optional[str] = Field(None, description="Source application info")
    campaign: Optional[str] = Field(None, description="Campaign information")
    page_type: Optional[str] = Field(None, description="Page type")
    page_title: Optional[str] = Field(None, description="Page title")
    app_download_date: Optional[str] = Field(None, description="Download date")
    unique_devices: int = Field(..., description="Unique device count")
    
    class Config:
        """Pydantic configuration."""
        str_strip_whitespace = True
        validate_assignment = True

# Model for Downloads Standard reports  
class AppleDownloadsData(AppleAnalyticsBaseData):
    """Raw Apple Downloads Standard data model."""
    
    download_type: str = Field(..., description="Type of download")
    page_type: Optional[str] = Field(None, description="Page type")
    pre_order: Optional[str] = Field(None, description="Pre-order information")
    
    class Config:
        """Pydantic configuration."""
        str_strip_whitespace = True
        validate_assignment = True

# Model for App Sessions reports
class AppleSessionsData(AppleAnalyticsBaseData):
    """Raw Apple Sessions data model."""
    
    page_type: Optional[str] = Field(None, description="Page type")
    app_download_date: Optional[str] = Field(None, description="App download date")
    sessions: int = Field(..., description="Number of app sessions")
    total_session_duration: int = Field(..., description="Total session duration in seconds")
    unique_devices: int = Field(..., description="Unique device count")
    
    @validator('sessions', 'total_session_duration')
    def validate_session_metrics(cls, v):
        """Ensure session metrics are non-negative."""
        if v < 0:
            raise ValueError("Session metrics must be non-negative")
        return v
    
    class Config:
        """Pydantic configuration."""
        str_strip_whitespace = True
        validate_assignment = True


# Keep the original for backward compatibility
AppleAnalyticsRawData = AppleInstallationData


class AppleAnalyticsNormalized(BaseModel):
    """Normalized Apple Analytics data model for Supabase storage."""
    
    # Primary identifiers
    unique_id: str = Field(..., description="Unique identifier for the record")
    app_id: str = Field(..., description="Apple App Store ID")
    report_date: datetime = Field(..., description="Report date")
    
    # Event details
    event_type: str = Field(..., description="Normalized event type")
    event_subtype: Optional[str] = Field(None, description="Event subtype (download type)")
    
    # App information
    app_name: str = Field(..., description="Application name")
    app_version: str = Field(..., description="App version")
    
    # Device and platform
    device_type: str = Field(..., description="Device type")
    platform_version: str = Field(..., description="Platform version")
    
    # Source attribution
    source_type: str = Field(..., description="Traffic source type")
    source_app: Optional[str] = Field(None, description="Source application")
    campaign_name: Optional[str] = Field(None, description="Campaign name")
    
    # Page context
    page_type: Optional[str] = Field(None, description="Page type")
    page_title: Optional[str] = Field(None, description="Page title")
    
    # Geographic data
    territory: str = Field(..., description="Country/territory code")
    
    # Metrics
    event_count: int = Field(..., description="Number of events")
    unique_device_count: int = Field(..., description="Number of unique devices")
    
    # Session metrics (for App Sessions reports)
    sessions: Optional[int] = Field(None, description="Number of app sessions")
    total_session_duration: Optional[int] = Field(None, description="Total session duration in seconds")
    avg_session_duration: Optional[float] = Field(None, description="Average session duration in seconds")
    
    # Metadata
    download_date: Optional[datetime] = Field(None, description="App download date")
    processed_at: datetime = Field(default_factory=datetime.utcnow, description="Processing timestamp")
    data_source: str = Field(default="apple_analytics", description="Data source identifier")
    
    @validator('unique_id')
    def validate_unique_id(cls, v):
        """Ensure unique_id is present."""
        if not v or len(v) < 10:
            raise ValueError("unique_id must be a valid hash")
        return v
    
    @validator('event_count', 'unique_device_count', 'sessions', 'total_session_duration')
    def validate_counts(cls, v):
        """Ensure counts are positive."""
        if v is not None and v < 0:
            raise ValueError("Counts must be non-negative")
        return v
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Supabase insertion."""
        data = self.dict()
        
        # Convert datetime objects to ISO strings
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        
        return data
    
    class Config:
        """Pydantic configuration."""
        str_strip_whitespace = True
        validate_assignment = True


def generate_unique_id(raw_data: AppleAnalyticsRawData) -> str:
    """Generate a unique identifier for the record."""
    # Create a composite key from ALL distinguishing fields to ensure uniqueness
    key_fields = [
        raw_data.date,
        raw_data.app_apple_identifier,
        raw_data.event,
        raw_data.download_type or "",
        raw_data.app_version,
        raw_data.device,
        raw_data.platform_version,
        raw_data.source_type,
        raw_data.source_info or "",
        raw_data.campaign or "",
        raw_data.page_type or "",
        raw_data.page_title or "",
        raw_data.app_download_date or "",
        raw_data.territory,
        str(raw_data.counts),
        str(raw_data.unique_devices)
    ]
    
    # Create hash from concatenated fields
    composite_key = "|".join(key_fields)
    return hashlib.sha256(composite_key.encode()).hexdigest()[:32]


def normalize_event_type(event: str, download_type: Optional[str] = None) -> tuple[str, Optional[str]]:
    """Normalize event type to standard values."""
    event_lower = event.lower().strip()
    
    # Map Apple events to normalized types
    event_mapping = {
        'install': 'install',
        'delete': 'uninstall',
        'update': 'update',
        'redownload': 'reinstall',
        'session': 'session',
    }
    
    normalized_event = event_mapping.get(event_lower, event_lower)
    
    # Use download_type as subtype for installs
    subtype = None
    if normalized_event == 'install' and download_type:
        subtype = download_type.lower().replace('-', '_').replace(' ', '_')
    
    return normalized_event, subtype


def normalize_device_type(device: str) -> str:
    """Normalize device type to standard values."""
    device_lower = device.lower().strip()
    
    device_mapping = {
        'iphone': 'iPhone',
        'ipad': 'iPad',
        'ipod': 'iPod',
        'mac': 'Mac',
        'apple watch': 'Apple Watch',
        'apple tv': 'Apple TV',
    }
    
    return device_mapping.get(device_lower, device)


def normalize_source_type(source_type: str) -> str:
    """Normalize source type to standard values."""
    source_lower = source_type.lower().strip()
    
    source_mapping = {
        'app store search': 'app_store_search',
        'app referrer': 'app_referrer',
        'web referrer': 'web_referrer',
        'organic search': 'organic_search',
        'paid search': 'paid_search',
        'direct': 'direct',
    }
    
    return source_mapping.get(source_lower, source_lower.replace(' ', '_'))


def parse_datetime_field(date_str: Optional[str]) -> Optional[datetime]:
    """Parse date string to datetime object."""
    if not date_str or date_str.strip() == '':
        return None
    
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            logger.warning(f"Unable to parse date: {date_str}")
            return None


def transform_apple_analytics_data(raw_data: AppleAnalyticsRawData) -> Optional[AppleAnalyticsNormalized]:
    """Transform raw Apple Analytics data to normalized format."""
    try:
        # Generate unique identifier
        unique_id = generate_unique_id(raw_data)
        
        # Parse dates
        report_date = parse_datetime_field(raw_data.date)
        download_date = parse_datetime_field(raw_data.app_download_date)
        
        if not report_date:
            logger.error(f"Invalid report date: {raw_data.date}")
            return None
        
        # Normalize event type
        event_type, event_subtype = normalize_event_type(raw_data.event, raw_data.download_type)
        
        # Create normalized record
        normalized = AppleAnalyticsNormalized(
            unique_id=unique_id,
            app_id=raw_data.app_apple_identifier,
            report_date=report_date,
            event_type=event_type,
            event_subtype=event_subtype,
            app_name=raw_data.app_name,
            app_version=raw_data.app_version,
            device_type=normalize_device_type(raw_data.device),
            platform_version=raw_data.platform_version,
            source_type=normalize_source_type(raw_data.source_type),
            source_app=raw_data.source_info,
            campaign_name=raw_data.campaign,
            page_type=raw_data.page_type,
            page_title=raw_data.page_title,
            territory=raw_data.territory,
            event_count=raw_data.counts,
            unique_device_count=raw_data.unique_devices,
            download_date=download_date,
        )
        
        return normalized
        
    except Exception as e:
        logger.error(f"Failed to transform Apple Analytics data: {e}")
        logger.error(f"Raw data: {raw_data}")
        return None


def parse_csv_row(row: Dict[str, str], report_name: str = "") -> Optional[AppleAnalyticsNormalized]:
    """Parse a CSV row into normalized data model, handling different report types."""
    try:
        # Common field mapping
        base_mapping = {
            'Date': 'date',
            'App Name': 'app_name', 
            'App Apple Identifier': 'app_apple_identifier',
            'App Version': 'app_version',
            'Device': 'device',
            'Platform Version': 'platform_version',
            'Source Type': 'source_type',
            'Territory': 'territory',
            'Counts': 'counts',
        }
        
        # Map common fields
        mapped_data = {}
        for csv_field, model_field in base_mapping.items():
            value = row.get(csv_field, '').strip()
            if value == '':
                value = None
            mapped_data[model_field] = value
        
        # Convert numeric fields
        try:
            mapped_data['counts'] = int(mapped_data['counts'] or 0)
        except (ValueError, TypeError):
            logger.warning(f"Invalid counts value in row: {row}")
            return None
        
        # Handle different report types
        if "Installation and Deletion" in report_name:
            # Installation/Deletion report has Event and Unique Devices
            mapped_data['event'] = row.get('Event', '').strip()
            mapped_data['unique_devices'] = int(row.get('Unique Devices', 0) or 0)
            mapped_data['download_type'] = row.get('Download Type', '').strip() or None
            mapped_data['source_info'] = row.get('Source Info', '').strip() or None
            mapped_data['campaign'] = row.get('Campaign', '').strip() or None
            mapped_data['page_type'] = row.get('Page Type', '').strip() or None
            mapped_data['page_title'] = row.get('Page Title', '').strip() or None
            mapped_data['app_download_date'] = row.get('App Download Date', '').strip() or None
            
        elif "Downloads Standard" in report_name:
            # Downloads report doesn't have Event, infer it from Download Type
            download_type = row.get('Download Type', '').strip()
            mapped_data['event'] = 'Install'  # All downloads are installs
            mapped_data['unique_devices'] = mapped_data['counts']  # Assume 1:1 for downloads
            mapped_data['download_type'] = download_type
            mapped_data['source_info'] = None
            mapped_data['campaign'] = None
            mapped_data['page_type'] = row.get('Page Type', '').strip() or None
            mapped_data['page_title'] = None
            mapped_data['app_download_date'] = None
            
        elif "Sessions" in report_name:
            # App Sessions report has Sessions, Total Session Duration, Unique Devices
            mapped_data['event'] = 'Session'  # All session reports are session events
            mapped_data['sessions'] = int(row.get('Sessions', 0) or 0)
            mapped_data['total_session_duration'] = int(row.get('Total Session Duration', 0) or 0)
            mapped_data['unique_devices'] = int(row.get('Unique Devices', 0) or 0)
            mapped_data['download_type'] = None
            mapped_data['source_info'] = None
            mapped_data['campaign'] = None
            mapped_data['page_type'] = row.get('Page Type', '').strip() or None
            mapped_data['page_title'] = None
            mapped_data['app_download_date'] = row.get('App Download Date', '').strip() or None
            
            # Calculate average session duration
            if mapped_data['sessions'] > 0:
                mapped_data['avg_session_duration'] = mapped_data['total_session_duration'] / mapped_data['sessions']
            else:
                mapped_data['avg_session_duration'] = 0.0
            
        else:
            # Default handling
            mapped_data['event'] = row.get('Event', 'Unknown').strip()
            mapped_data['unique_devices'] = int(row.get('Unique Devices', mapped_data['counts']) or 0)
            mapped_data['download_type'] = row.get('Download Type', '').strip() or None
            mapped_data['source_info'] = row.get('Source Info', '').strip() or None
            mapped_data['campaign'] = row.get('Campaign', '').strip() or None
            mapped_data['page_type'] = row.get('Page Type', '').strip() or None  
            mapped_data['page_title'] = row.get('Page Title', '').strip() or None
            mapped_data['app_download_date'] = row.get('App Download Date', '').strip() or None
        
        # Create raw data object for unique ID generation
        class RawData:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)
        
        raw_data = RawData(mapped_data)
        
        # Transform to normalized format
        return transform_raw_to_normalized(raw_data, report_name)
        
    except Exception as e:
        logger.error(f"Failed to parse CSV row: {e}")
        logger.error(f"Row data: {row}")
        return None


def transform_raw_to_normalized(raw_data, report_name: str) -> Optional[AppleAnalyticsNormalized]:
    """Transform raw data to normalized format."""
    try:
        # Generate unique identifier
        unique_id = generate_unique_id_from_dict(raw_data.__dict__)
        
        # Parse dates
        report_date = parse_datetime_field(raw_data.date)
        download_date = parse_datetime_field(raw_data.app_download_date)
        
        if not report_date:
            logger.error(f"Invalid report date: {raw_data.date}")
            return None
        
        # Normalize event type
        event_type, event_subtype = normalize_event_type(raw_data.event, raw_data.download_type)
        
        # Get session data if available
        sessions = getattr(raw_data, 'sessions', None)
        total_session_duration = getattr(raw_data, 'total_session_duration', None)
        avg_session_duration = getattr(raw_data, 'avg_session_duration', None)
        
        # Create normalized record
        normalized = AppleAnalyticsNormalized(
            unique_id=unique_id,
            app_id=raw_data.app_apple_identifier,
            report_date=report_date,
            event_type=event_type,
            event_subtype=event_subtype,
            app_name=raw_data.app_name,
            app_version=raw_data.app_version,
            device_type=normalize_device_type(raw_data.device),
            platform_version=raw_data.platform_version,
            source_type=normalize_source_type(raw_data.source_type),
            source_app=raw_data.source_info,
            campaign_name=raw_data.campaign,
            page_type=raw_data.page_type,
            page_title=raw_data.page_title,
            territory=raw_data.territory,
            event_count=raw_data.counts,
            unique_device_count=raw_data.unique_devices,
            sessions=sessions,
            total_session_duration=total_session_duration,
            avg_session_duration=avg_session_duration,
            download_date=download_date,
            data_source=f"apple_analytics_{report_name.lower().replace(' ', '_')}"
        )
        
        return normalized
        
    except Exception as e:
        logger.error(f"Failed to transform to normalized format: {e}")
        return None


def generate_unique_id_from_dict(data_dict: dict) -> str:
    """Generate unique ID from dictionary data."""
    key_fields = [
        str(data_dict.get('date', '')),
        str(data_dict.get('app_apple_identifier', '')),
        str(data_dict.get('event', '')),
        str(data_dict.get('download_type', '') or ''),
        str(data_dict.get('app_version', '')),
        str(data_dict.get('device', '')),
        str(data_dict.get('platform_version', '')),
        str(data_dict.get('source_type', '')),
        str(data_dict.get('source_info', '') or ''),
        str(data_dict.get('campaign', '') or ''),
        str(data_dict.get('page_type', '') or ''),
        str(data_dict.get('page_title', '') or ''),
        str(data_dict.get('app_download_date', '') or ''),
        str(data_dict.get('territory', '')),
        str(data_dict.get('counts', 0)),
        str(data_dict.get('unique_devices', 0))
    ]
    
    composite_key = "|".join(key_fields)
    return hashlib.sha256(composite_key.encode()).hexdigest()[:32]


def transform_csv_data(csv_content: str, report_name: str = "") -> List[AppleAnalyticsNormalized]:
    """Transform CSV content to list of normalized data models."""
    import csv
    import io
    
    transformed_data = []
    failed_count = 0
    
    try:
        # Parse CSV
        csv_reader = csv.DictReader(io.StringIO(csv_content), delimiter='\t')
        
        for row_num, row in enumerate(csv_reader, 1):
            # Parse and transform row directly to normalized format
            normalized_data = parse_csv_row(row, report_name)
            if normalized_data:
                transformed_data.append(normalized_data)
            else:
                failed_count += 1
        
        logger.info(f"Processed {len(transformed_data)} records successfully, {failed_count} failed for report: {report_name}")
        return transformed_data
        
    except Exception as e:
        logger.error(f"Failed to transform CSV data for {report_name}: {e}")
        return []