"""
Apple Analytics Reports DAG

This DAG fetches analytics reports from Apple App Store Connect API
and stores them in Supabase for analysis.

Key Features:
- Fetches multiple analytics reports (app usage, downloads, performance)
- Transforms data to normalized format using Pydantic models
- Uploads to Supabase with conflict resolution
- Comprehensive logging and error handling
- Secret Manager integration for credentials

Schedule: Daily at 4 AM UTC
Catchup: False (only process current data)
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import logging

# Import our task functions
from dependencies.apple.apple_tasks import (
    fetch_apple_analytics_data,
    transform_apple_analytics_data,
    upload_to_supabase,
    validate_apple_credentials,
)

# Import configuration
from dependencies.apple.apple_config import DAG_CONFIG

logger = logging.getLogger(__name__)

# DAG Configuration
default_args = {
    "owner": DAG_CONFIG["owner"],
    "depends_on_past": DAG_CONFIG["depends_on_past"],
    "start_date": datetime.strptime(DAG_CONFIG["start_date"], "%Y-%m-%d"),
    "email_on_failure": DAG_CONFIG["email_on_failure"],
    "email_on_retry": DAG_CONFIG["email_on_retry"],
    "retries": DAG_CONFIG["retries"],
    "retry_delay": timedelta(minutes=DAG_CONFIG["retry_delay_minutes"]),
    "email": ["<EMAIL>"],
}

# Create the DAG
dag = DAG(
    DAG_CONFIG["dag_id"],
    default_args=default_args,
    description="Apple Analytics reports processing with comprehensive data transformation",
    schedule_interval=DAG_CONFIG["schedule_interval"],  # Daily at 4 AM UTC
    start_date=datetime.strptime(DAG_CONFIG["start_date"], "%Y-%m-%d"),
    catchup=DAG_CONFIG["catchup"],
    tags=DAG_CONFIG["tags"],
    max_active_runs=DAG_CONFIG["max_active_runs"],
)

# Task 1: Validate Apple API credentials
validate_credentials_task = PythonOperator(
    task_id="validate_apple_credentials",
    python_callable=validate_apple_credentials,
    dag=dag,
    doc_md="""
    ## Validate Apple API Credentials
    
    Validates Apple API credentials and connectivity before processing.
    
    **What it does:**
    - Tests credential retrieval from environment variables and Secret Manager
    - Generates JWT token for API authentication
    - Performs test API call to verify connectivity
    - Returns validation status and available reports count
    
    **Dependencies:**
    - Apple API credentials (key ID, issuer ID, private key, app ID)
    - Google Secret Manager access
    - Network connectivity to Apple API
    """,
)

# Task 2: Fetch Apple Analytics data
fetch_task = PythonOperator(
    task_id="fetch_apple_analytics_data",
    python_callable=fetch_apple_analytics_data,
    dag=dag,
    doc_md="""
    ## Fetch Apple Analytics Data
    
    Fetches analytics reports from Apple App Store Connect API.
    
    **What it does:**
    - Uses existing ONGOING report request to get latest data
    - Fetches all enabled reports (app usage, downloads, performance)
    - Downloads and decompresses report data
    - Returns structured data for transformation
    
    **Target Reports:**
    - App Usage Standard: Core usage metrics
    - App Downloads Standard: Download metrics
    - App Store Installation and Deletion Detailed: Detailed install/uninstall data
    - App Performance Standard: Performance and crash data
    
    **Output:**
    - Raw CSV content for each report
    - Metadata about report IDs and instances
    - Fetch statistics and timestamps
    """,
)

# Task 3: Transform Apple Analytics data
transform_task = PythonOperator(
    task_id="transform_apple_analytics_data",
    python_callable=transform_apple_analytics_data,
    dag=dag,
    doc_md="""
    ## Transform Apple Analytics Data
    
    Transforms raw Apple Analytics data to normalized format using Pydantic models.
    
    **What it does:**
    - Parses CSV content from fetched reports
    - Validates data using Pydantic models
    - Normalizes event types, device types, and source types
    - Generates unique identifiers for deduplication
    - Converts dates to proper datetime objects
    - Filters out invalid or test data
    
    **Data Transformations:**
    - Event type normalization (Install → install, Delete → uninstall)
    - Device type standardization (iPhone, iPad, Mac, etc.)
    - Source type classification (app_store_search, app_referrer, etc.)
    - Territory code validation
    - Metrics validation (positive counts only)
    
    **Output:**
    - Normalized Apple Analytics data ready for database insertion
    - Transformation statistics per report
    - Data quality metrics
    """,
)

# Task 4: Upload to Supabase
upload_task = PythonOperator(
    task_id="upload_to_supabase",
    python_callable=upload_to_supabase,
    dag=dag,
    doc_md="""
    ## Upload to Supabase
    
    Uploads transformed Apple Analytics data to Supabase database.
    
    **What it does:**
    - Connects to Supabase using service role credentials
    - Uploads data in batches to prevent timeouts
    - Uses upsert to handle duplicates gracefully
    - Provides detailed upload statistics
    - Handles partial failures gracefully
    
    **Database Operations:**
    - Table: apple_analytics_data
    - Batch size: 100 records per batch
    - Conflict resolution: Upsert on unique_id
    - Retry logic: 3 attempts with backoff
    
    **Upload Statistics:**
    - Total records processed
    - Successfully uploaded count
    - Failed batch count and details
    - Upload timestamps
    """,
)

# Define task dependencies - simple linear flow
validate_credentials_task >> fetch_task >> transform_task >> upload_task

# Add DAG documentation
dag.doc_md = """
# Apple Analytics Reports DAG

This DAG processes Apple App Store Connect analytics reports and stores them in Supabase.

## Key Features

- **Credential Validation**: Validates Apple API credentials and connectivity
- **Multi-Report Fetching**: Fetches multiple analytics reports (usage, downloads, performance)
- **Data Transformation**: Transforms raw data to normalized format with Pydantic validation
- **Supabase Integration**: Uploads data with conflict resolution and batch processing
- **Error Handling**: Comprehensive error handling with partial failure support
- **Secret Manager**: Integrates with Google Secret Manager for credential management

## Data Flow

1. **Validate**: Check Apple API credentials and connectivity
2. **Fetch**: Download latest analytics reports from Apple API
3. **Transform**: Parse CSV data and transform to normalized format
4. **Upload**: Store in Supabase with deduplication

## Reports Processed

- **App Usage Standard**: Core app usage metrics including active users
- **App Downloads Standard**: Download and installation metrics
- **App Store Installation and Deletion Detailed**: Detailed install/uninstall data
- **App Performance Standard**: Performance metrics and crash data

## Configuration

- **Schedule**: Daily at 4 AM UTC (after other data pipelines)
- **Retries**: 2 attempts with 10-minute delays
- **Batch Size**: 100 records per Supabase upload
- **Lookback**: Uses existing ONGOING report for latest data
- **Deduplication**: Based on composite unique_id hash

## Dependencies

- Apple API credentials in Secret Manager or environment variables
- Supabase connection configured via Secret Manager
- Required packages: requests, pydantic, supabase-py, google-cloud-secret-manager

## Monitoring

- Email notifications on failures
- Detailed logging for each step
- Task-level error handling and recovery
- Upload statistics and data quality metrics

## Target Reports Configuration

Reports are configured in `apple_config.py` with priority levels:
- Priority 1: Core analytics (app usage, downloads)
- Priority 2: Detailed metrics (installation/deletion details)
- Priority 3: Performance metrics
- Priority 5: Framework usage (disabled by default)

## Supabase Schema

The data is stored in the `apple_analytics_data` table with the following key fields:
- `unique_id`: Composite hash for deduplication
- `app_id`: Apple App Store ID
- `report_date`: Date of the report
- `event_type`: Normalized event type (install, uninstall, etc.)
- `device_type`: Device type (iPhone, iPad, etc.)
- `source_type`: Traffic source (app_store_search, app_referrer, etc.)
- `territory`: Country/region code
- `event_count`: Number of events
- `unique_device_count`: Number of unique devices
"""

if __name__ == "__main__":
    dag.test()