from airflow import DAG
from airflow.operators.python import PythonOperator
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from datetime import datetime
import csv
from google.oauth2 import service_account
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google.auth import default
from google.cloud import storage
from googleapiclient.discovery import build
import logging
import os
from airflow.utils.log.logging_mixin import LoggingMixin

from dependencies.utils.misc import IS_DEV
from dependencies.sync_backup_sheet.utils import (
    backup_sheet_to_gcs,
    update_brand_alias_mappings,
)

# Configure logging

logger = logging.getLogger(__name__)

# DAG default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 0,
    'email': '<EMAIL>',
}

with DAG(
    'sync_backup_sheets',
    default_args=default_args,
    description='Backup first-hand,second-hand and brand-domain-alias Google Sheets to GCS',
    schedule='0 * * * *' if not IS_DEV() else None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['sheets', 'gcs', 'backup', 'firsthand', 'secondhand', 'brand-domain-alias'],
) as dag:

    backup_firsthand_task = PythonOperator(
        task_id='backup_firsthand_sheet_to_gcs',
        python_callable=backup_sheet_to_gcs,
        op_kwargs={
            'sheet_type': 'firsthand',
            'spreadsheet_id': '1jbXEZeif0hjCfuW4XF492S4IQ4_GBctuwsk5RJ3Dhdc',
            'data_range': 'A:A',
        }
    )

    backup_secondhand_task = PythonOperator(
        task_id='backup_secondhand_sheet_to_gcs',
        python_callable=backup_sheet_to_gcs,
        op_kwargs={
            'sheet_type': 'secondhand',
            'spreadsheet_id': '1xzOB8XqnwdntBsxNDYmjGiGjq8Y7QjHWpt8jucDkoBQ',
            'data_range': 'A:A',
        }
    )

    backup_brand_domain_task = PythonOperator(
        task_id='backup_brand_domain_alia_sheet_to_gcs',
        python_callable=backup_sheet_to_gcs,
        op_kwargs={
            'sheet_type': 'brand-domain-alias',
            'spreadsheet_id': '1aNUzzsQE54sbdTn31AoChmjbZ95fu04SxNMmGUeWz_Y',
            'data_range': 'A:C',
        },
    )
    
    sync_mappings_task = PythonOperator(
        task_id='sync_brand_alias_to_bigtable',
        python_callable=update_brand_alias_mappings
    )
    
    backup_brand_domain_task >> sync_mappings_task
